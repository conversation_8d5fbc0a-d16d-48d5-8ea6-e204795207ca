<?php

namespace App\Livewire\Invoice;

use App\Exports\ManagementReportExport;
use App\Models\Invoice;
use Livewire\Component;
use Maatwebsite\Excel\Facades\Excel;

class InvoiceReports extends Component
{
    public $report_type = 'summary';

    public $date_from;

    public $date_to;

    public $status = 'all';

    public $estate_id = null;

    public function mount()
    {
        $this->date_from = now()->startOfMonth()->format('Y-m-d');
        $this->date_to = now()->endOfMonth()->format('Y-m-d');
    }

    public function generateReport()
    {
        $this->validate([
            'report_type' => 'required|in:summary,detailed,overdue,payments',
            'date_from' => 'required|date',
            'date_to' => 'required|date|after:date_from',
            'status' => 'nullable|in:all,draft,sent,paid,overdue,cancelled',
            'estate_id' => 'nullable|exists:estates,id',
        ]);

        $query = Invoice::query()
            ->with(['house.estate', 'house.contacts'])
            ->whereBetween('billing_period_start', [$this->date_from, $this->date_to]);

        if ($this->status !== 'all') {
            $query->where('status', $this->status);
        }

        if ($this->estate_id) {
            $query->whereHas('house', function ($q) {
                $q->where('estate_id', $this->estate_id);
            });
        }

        $invoices = $query->get();

        return Excel::download(new ManagementReportExport($invoices), 'invoice-report.xlsx');
    }

    public function render()
    {
        return view('livewire.invoice.invoice-reports')
            ->layout('layouts.app');
    }
}
