<?php

namespace App\Providers;

use App\Enums\UserRole;
use App\Models\Invoice;
use App\Policies\InvoicePolicy;
use App\Services\PermissionValidationService;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        Invoice::class => InvoicePolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        $this->registerPolicies();

        // Register all permissions from UserRole enum
        $this->registerAllPermissions();

        // Register role-based gates
        $this->registerRoleGates();

        // Register estate-based gates
        $this->registerEstateGates();
    }

    /**
     * Register all permissions from UserRole enum
     */
    private function registerAllPermissions(): void
    {
        // Get all unique permissions from all roles
        $allPermissions = collect(UserRole::cases())
            ->flatMap(fn ($role) => $role->permissions())
            ->unique()
            ->values()
            ->all();

        // Register each permission as a gate
        foreach ($allPermissions as $permission) {
            Gate::define($permission, function ($user) use ($permission) {
                return $user->hasPermission($permission);
            });
        }
    }

    /**
     * Register role-based gates
     */
    private function registerRoleGates(): void
    {
        Gate::define('is-admin', function ($user) {
            return $user->hasRole(UserRole::ADMIN);
        });

        Gate::define('is-manager', function ($user) {
            return $user->hasRole(UserRole::MANAGER);
        });

        Gate::define('is-reviewer', function ($user) {
            return $user->hasRole(UserRole::REVIEWER);
        });

        Gate::define('is-caretaker', function ($user) {
            return $user->hasRole(UserRole::CARETAKER);
        });

        Gate::define('is-resident', function ($user) {
            return $user->hasRole(UserRole::RESIDENT);
        });

        // Management hierarchy gates
        Gate::define('manage-users', function ($user, $targetUser = null) {
            if (! $targetUser) {
                return $user->hasPermission('users.assign_roles');
            }

            return app(PermissionValidationService::class)->canManageUser($user, $targetUser);
        });

        Gate::define('assign-estates', function ($user) {
            return $user->hasPermission('users.assign_estates');
        });
    }

    /**
     * Register estate-based gates
     */
    private function registerEstateGates(): void
    {
        Gate::define('access-estate', function ($user, $estate) {
            return app(PermissionValidationService::class)->canAccessEstate($user, $estate);
        });

        Gate::define('access-house', function ($user, $house) {
            return app(PermissionValidationService::class)->canAccessHouse($user, $house);
        });

        Gate::define('manage-estate', function ($user, $estate) {
            if (! app(PermissionValidationService::class)->canAccessEstate($user, $estate)) {
                return false;
            }

            return $user->hasPermission('estates.manage_assigned') || $user->hasPermission('estates.manage_all');
        });

        Gate::define('view-estate-data', function ($user, $estate) {
            if (! app(PermissionValidationService::class)->canAccessEstate($user, $estate)) {
                return false;
            }

            return $user->hasPermission('estates.view_assigned') || $user->hasPermission('estates.view_all');
        });
    }
}
