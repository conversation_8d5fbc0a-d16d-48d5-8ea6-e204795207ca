<?php

use App\Enums\UserRole;
use App\Livewire\Admin\BillingManager;
use App\Models\Estate;
use App\Models\House;
use App\Models\Invoice;
use App\Models\User;
use Livewire\Livewire;

beforeEach(function () {
    // Create test data
    $this->user = User::factory()->create(['role' => UserRole::MANAGER]);
    $this->estate = Estate::factory()->create();
    $this->houses = House::factory()->count(3)->create(['estate_id' => $this->estate->id]);
});

test('it renders billing manager dashboard', function () {
    Livewire::actingAs($this->user)
        ->test(BillingManager::class)
        ->assertStatus(200);
});

test('it displays billing summary', function () {
    // Create invoices
    foreach ($this->houses as $house) {
        Invoice::factory()->create([
            'house_id' => $house->id,
            'status' => 'draft',
            'amount' => 1000,
        ]);
    }

    Livewire::actingAs($this->user)
        ->test(BillingManager::class)
        ->assertSee('Total Houses')
        ->assertSee('Total Invoices')
        ->assertSee('3')
        ->assertSee('3000');
});

test('it can filter invoices by status', function () {
    // Create invoices with different statuses
    Invoice::factory()->create([
        'house_id' => $this->houses[0]->id,
        'status' => 'draft',
        'amount' => 1000,
    ]);

    Invoice::factory()->create([
        'house_id' => $this->houses[1]->id,
        'status' => 'overdue',
        'amount' => 1500,
    ]);

    Invoice::factory()->create([
        'house_id' => $this->houses[2]->id,
        'status' => 'paid',
        'amount' => 1200,
    ]);

    Livewire::actingAs($this->user)
        ->test(BillingManager::class)
        ->set('statusFilter', 'overdue')
        ->assertSee('1500')
        ->assertDontSee('1000')
        ->assertDontSee('1200');
});

test('it can select multiple invoices for bulk operations', function () {
    $invoices = [];
    foreach ($this->houses as $house) {
        $invoices[] = Invoice::factory()->create([
            'house_id' => $house->id,
            'status' => 'draft',
        ]);
    }

    Livewire::actingAs($this->user)
        ->test(BillingManager::class)
        ->set('selectedInvoices', [$invoices[0]->id, $invoices[2]->id])
        ->assertSet('selectedInvoices', [$invoices[0]->id, $invoices[2]->id]);
});

test('it can generate bulk invoices from dashboard', function () {
    $admin = User::factory()->create(['role' => UserRole::ADMIN]);

    Livewire::actingAs($admin)
        ->test(BillingManager::class)
        ->set('estateFilter', $this->estate->id)
        ->set('bulkInvoiceAmount', 1500)
        ->set('bulkInvoiceDescription', 'Test Bulk Invoice')
        ->call('generateBulkInvoices')
        ->assertDispatched('invoice-generated');

    $this->assertDatabaseHas('invoices', [
        'amount' => 1500,
        'notes' => 'Test Bulk Invoice',
        'status' => 'draft',
    ]);
});

test('it can send bulk reminders from dashboard', function () {
    $invoices = [];
    foreach ($this->houses as $house) {
        $invoices[] = Invoice::factory()->create([
            'house_id' => $house->id,
            'status' => 'overdue',
        ]);
    }

    Livewire::actingAs($this->user)
        ->test(BillingManager::class)
        ->set('selectedInvoices', [$invoices[0]->id, $invoices[1]->id])
        ->set('bulkReminderMessage', 'Test reminder message')
        ->call('sendBulkReminders')
        ->assertDispatched('reminders-sent');
});

test('it can bulk approve invoices from dashboard', function () {
    $reviewer = User::factory()->create(['role' => UserRole::REVIEWER]);

    $invoices = [];
    foreach ($this->houses as $house) {
        $invoices[] = Invoice::factory()->create([
            'house_id' => $house->id,
            'status' => 'submitted',
        ]);
    }

    Livewire::actingAs($reviewer)
        ->test(BillingManager::class)
        ->set('selectedInvoices', [$invoices[0]->id, $invoices[1]->id])
        ->call('bulkApproveInvoices')
        ->assertDispatched('invoices-approved');

    foreach ($invoices as $invoice) {
        expect($invoice->fresh()->status)->toBe('approved');
    }
});

test('it validates user permissions for bulk operations', function () {
    $caretaker = User::factory()->create(['role' => UserRole::CARETAKER]);

    $invoices = [];
    foreach ($this->houses as $house) {
        $invoices[] = Invoice::factory()->create([
            'house_id' => $house->id,
            'status' => 'submitted',
        ]);
    }

    Livewire::actingAs($caretaker)
        ->test(BillingManager::class)
        ->set('selectedInvoices', [$invoices[0]->id, $invoices[1]->id])
        ->call('bulkApproveInvoices')
        ->assertDispatched('permission-denied');
});

test('it displays aging analysis', function () {
    // Create overdue invoices
    Invoice::factory()->create([
        'house_id' => $this->houses[0]->id,
        'status' => 'overdue',
        'due_date' => now()->subDays(10),
        'amount' => 1000,
    ]);

    Invoice::factory()->create([
        'house_id' => $this->houses[1]->id,
        'status' => 'overdue',
        'due_date' => now()->subDays(40),
        'amount' => 1500,
    ]);

    Livewire::actingAs($this->user)
        ->test(BillingManager::class)
        ->assertSee('Aging Analysis')
        ->assertSee('0-30 Days')
        ->assertSee('31-60 Days')
        ->assertSee('1000')
        ->assertSee('1500');
});

test('it can export billing data', function () {
    foreach ($this->houses as $house) {
        Invoice::factory()->create([
            'house_id' => $house->id,
            'status' => 'overdue',
            'amount' => 1000,
        ]);
    }

    Livewire::actingAs($this->user)
        ->test(BillingManager::class)
        ->call('exportBillingData')
        ->assertDispatched('billing-data-exported');
});

test('it refreshes data automatically', function () {
    Livewire::actingAs($this->user)
        ->test(BillingManager::class)
        ->call('refreshData')
        ->assertSet('lastRefresh', now()->diffForHumans());
});

test('it handles invoice selection toggling', function () {
    $invoice = Invoice::factory()->create([
        'house_id' => $this->houses[0]->id,
        'status' => 'draft',
    ]);

    $component = Livewire::actingAs($this->user)
        ->test(BillingManager::class);

    // Select invoice
    $component->call('toggleInvoiceSelection', $invoice->id)
        ->assertSet('selectedInvoices', [$invoice->id]);

    // Deselect invoice
    $component->call('toggleInvoiceSelection', $invoice->id)
        ->assertSet('selectedInvoices', []);
});

test('it can select all invoices', function () {
    $invoices = [];
    foreach ($this->houses as $house) {
        $invoices[] = Invoice::factory()->create([
            'house_id' => $house->id,
            'status' => 'draft',
        ]);
    }

    $invoiceIds = collect($invoices)->pluck('id')->toArray();

    Livewire::actingAs($this->user)
        ->test(BillingManager::class)
        ->call('selectAllInvoices')
        ->assertSet('selectedInvoices', $invoiceIds);
});

test('it can clear selection', function () {
    $invoices = [];
    foreach ($this->houses as $house) {
        $invoices[] = Invoice::factory()->create([
            'house_id' => $house->id,
            'status' => 'draft',
        ]);
    }

    $invoiceIds = collect($invoices)->pluck('id')->toArray();

    Livewire::actingAs($this->user)
        ->test(BillingManager::class)
        ->set('selectedInvoices', $invoiceIds)
        ->call('clearSelection')
        ->assertSet('selectedInvoices', []);
});
