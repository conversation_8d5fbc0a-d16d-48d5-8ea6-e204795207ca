<?php

declare(strict_types=1);

use App\Enums\UserRole;
use App\Livewire\Admin\InvoiceApproval;
use App\Models\Estate;
use App\Models\House;
use App\Models\Invoice;
use App\Models\MeterReading;
use App\Models\User;
use App\Models\WaterRate;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;

uses(RefreshDatabase::class);

test('renders invoice approval dashboard with pending invoices', function () {
    $estate = Estate::factory()->create();
    $house = House::factory()->create(['estate_id' => $estate->id]);
    $waterRate = WaterRate::factory()->create(['estate_id' => $estate->id]);
    $meterReading = MeterReading::factory()->create(['house_id' => $house->id]);
    $manager = User::factory()->create(['role' => UserRole::MANAGER]);
    $reviewer = User::factory()->create(['role' => UserRole::REVIEWER]);

    $draftInvoice = Invoice::factory()->create([
        'house_id' => $house->id,
        'water_rate_id' => $waterRate->id,
        'meter_reading_id' => $meterReading->id,
        'status' => 'draft',
    ]);

    $submittedInvoice = Invoice::factory()->create([
        'house_id' => $house->id,
        'water_rate_id' => $waterRate->id,
        'meter_reading_id' => $meterReading->id,
        'status' => 'submitted',
        'submitted_by' => $manager->id,
        'submitted_at' => now(),
    ]);

    $approvedInvoice = Invoice::factory()->create([
        'house_id' => $house->id,
        'water_rate_id' => $waterRate->id,
        'meter_reading_id' => $meterReading->id,
        'status' => 'approved',
        'approved_by' => $reviewer->id,
        'approved_at' => now(),
    ]);

    Livewire::actingAs($reviewer)
        ->test(InvoiceApproval::class)
        ->assertSet('filter', 'pending')
        ->assertSee('Invoice Approval')
        ->assertSee($draftInvoice->invoice_number)
        ->assertSee($submittedInvoice->invoice_number)
        ->assertDontSee($approvedInvoice->invoice_number);
});

    it('renders invoice approval dashboard with pending invoices', function () {
        // Create invoices in different states
        $draftInvoice = Invoice::factory()->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'meter_reading_id' => $this->meterReading->id,
            'status' => 'draft',
        ]);

        $submittedInvoice = Invoice::factory()->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'meter_reading_id' => $this->meterReading->id,
            'status' => 'submitted',
            'submitted_by' => $this->manager->id,
            'submitted_at' => now(),
        ]);

        $approvedInvoice = Invoice::factory()->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'meter_reading_id' => $this->meterReading->id,
            'status' => 'approved',
            'approved_by' => $this->reviewer->id,
            'approved_at' => now(),
        ]);

        Livewire::actingAs($this->reviewer)
            .test(InvoiceApproval::class)
            .assertSet('filter', 'pending')
            .assertSee('Invoice Approval')
                ->assertSee($draftInvoice->invoice_number)
                ->assertSee($submittedInvoice->invoice_number)
            .assertDontSee($approvedInvoice->invoice_number);
    });

    it('can filter invoices by status', function () {
        $draftInvoice = Invoice::factory()->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'meter_reading_id' => $this->meterReading->id,
            'status' => 'draft',
        ]);

        $submittedInvoice = Invoice::factory()->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'meter_reading_id' => $this->meterReading->id,
            'status' => 'submitted',
            'submitted_by' => $this->manager->id,
            'submitted_at' => now(),
        ]);

        Livewire::actingAs($this->reviewer)
            .test(InvoiceApproval::class)
            .assertSet('filter', 'pending')
                ->assertSee($draftInvoice->invoice_number)
                ->assertSee($submittedInvoice->invoice_number)
            .set('filter', 'draft')
                ->assertSee($draftInvoice->invoice_number)
                ->assertDontSee($submittedInvoice->invoice_number)
            .set('filter', 'submitted')
                ->assertDontSee($draftInvoice->invoice_number)
                ->assertSee($submittedInvoice->invoice_number);
    });

    it('can submit invoice for approval', function () {
        $invoice = Invoice::factory()->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'meter_reading_id' => $this->meterReading->id,
            'status' => 'draft',
        ]);

        Livewire::actingAs($this->manager)
            .test(InvoiceApproval::class)
            .call('submitForApproval', $invoice->id)
            .assertDispatched('invoice-submitted');

        $this->assertDatabaseHas('invoices', [
            'id' => $invoice->id,
            'status' => 'submitted',
            'submitted_by' => $this->manager->id,
            'submitted_at' => now(),
        ]);
    });

    it('cannot submit invoice for approval without proper permissions', function () {
        $invoice = Invoice::factory()->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'meter_reading_id' => $this->meterReading->id,
            'status' => 'draft',
        ]);

        Livewire::actingAs($this->caretaker)
            .test(InvoiceApproval::class)
            .call('submitForApproval', $invoice->id)
            .assertForbidden();

        $this->assertDatabaseHas('invoices', [
            'id' => $invoice->id,
            'status' => 'draft',
            'submitted_by' => null,
            'submitted_at' => null,
        ]);
    });

    it('can approve invoice', function () {
        $invoice = Invoice::factory()->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'meter_reading_id' => $this->meterReading->id,
            'status' => 'submitted',
            'submitted_by' => $this->manager->id,
            'submitted_at' => now(),
        ]);

        Livewire::actingAs($this->reviewer)
            .test(InvoiceApproval::class)
            .call('approve', $invoice->id)
                ->assertDispatched('invoice-approved');

        $this->assertDatabaseHas('invoices', [
            'id' => $invoice->id,
            'status' => 'approved',
            'approved_by' => $this->reviewer->id,
            'approved_at' => now(),
        ]);
    });

    it('cannot approve invoice without proper permissions', function () {
        $invoice = Invoice::factory()->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'meter_reading_id' => $this->meterReading->id,
            'status' => 'submitted',
            'submitted_by' => $this->manager->id,
            'submitted_at' => now(),
        ]);

        Livewire::actingAs($this->caretaker)
            .test(InvoiceApproval::class)
            .call('approve', $invoice->id)
                ->assertForbidden();

        $this->assertDatabaseHas('invoices', [
            'id' => $invoice->id,
            'status' => 'submitted',
            'approved_by' => null,
            'approved_at' => null,
        ]);
    });

    it('can reject invoice', function () {
        $invoice = Invoice::factory()->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'meter_reading_id' => $this->meterReading->id,
            'status' => 'submitted',
            'submitted_by' => $this->manager->id,
            'submitted_at' => now(),
        ]);

        Livewire::actingAs($this->reviewer)
            .test(InvoiceApproval::class)
            .set('rejectionReason', 'Incorrect meter reading')
            .call('reject', $invoice->id)
                ->assertDispatched('invoice-rejected')
                ->assertSet('rejectionReason', '');

        $this->assertDatabaseHas('invoices', [
            'id' => $invoice->id,
            'status' => 'draft',
        ]);
    });

    it('can send invoice after approval', function () {
        $invoice = Invoice::factory()->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'meter_reading_id' => $this->meterReading->id,
            'status' => 'approved',
            'approved_by' => $this->reviewer->id,
            'approved_at' => now(),
            'pdf_path' => 'invoices/test.pdf',
        ]);

        Livewire::actingAs($this->manager)
            .test(InvoiceApproval::class)
            .call('sendInvoice', $invoice->id)
                ->assertDispatched('invoice-sent');

        $this->assertDatabaseHas('invoices', [
            'id' => $invoice->id,
            'status' => 'sent',
            'sent_at' => now(),
        ]);
    });

    it('can view invoice details', function () {
        $invoice = Invoice::factory()->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'meter_reading_id' => $this->meterReading->id,
            'status' => 'submitted',
            'submitted_by' => $this->manager->id,
            'submitted_at' => now(),
        ]);

        Livewire::actingAs($this->reviewer)
            .test(InvoiceApproval::class)
            .call('viewInvoice', $invoice->id)
                ->assertDispatched('show-invoice-details', invoiceId: $invoice->id);
    });

    it('shows correct statistics', function () {
        // Create invoices in different states
        Invoice::factory()->count(3)->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'meter_reading_id' => $this->meterReading->id,
            'status' => 'draft',
        ]);

        Invoice::factory()->count(2)->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'meter_reading_id' => $this->meterReading->id,
            'status' => 'submitted',
            'submitted_by' => $this->manager->id,
            'submitted_at' => now(),
        ]);

        Invoice::factory()->count(1)->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'meter_reading_id' => $this->meterReading->id,
            'status' => 'approved',
            'approved_by' => $this->reviewer->id,
            'approved_at' => now(),
        ]);

        Livewire::actingAs($this->reviewer)
            .test(InvoiceApproval::class)
                ->assertSee('5') // Total pending (3 draft + 2 submitted)
                ->assertSee('3') // Draft count
                ->assertSee('2') // Submitted count
                ->assertSee('1'); // Approved count
    });

    it('can search invoices by invoice number', function () {
        $invoice1 = Invoice::factory()->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'meter_reading_id' => $this->meterReading->id,
            'status' => 'draft',
            'invoice_number' => 'INV-2025-001',
        ]);

        $invoice2 = Invoice::factory()->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'meter_reading_id' => $this->meterReading->id,
            'status' => 'draft',
            'invoice_number' => 'INV-2025-002',
        ]);

        Livewire::actingAs($this->reviewer)
            .test(InvoiceApproval::class)
                ->set('search', 'INV-2025-001')
                ->assertSee($invoice1->invoice_number)
                ->assertDontSee($invoice2->invoice_number)
                ->set('search', 'INV-2025-002')
                ->assertDontSee($invoice1->invoice_number)
                ->assertSee($invoice2->invoice_number);
    });

    it('validates rejection reason is required', function () {
        $invoice = Invoice::factory()->create([
            'house_id' => $this->house->id,
            'water_rate_id' => $this->waterRate->id,
            'meter_reading_id' => $this->meterReading->id,
            'status' => 'submitted',
            'submitted_by' => $this->manager->id,
            'submitted_at' => now(),
        ]);

        Livewire::actingAs($this->reviewer)
            .test(InvoiceApproval::class)
                ->set('rejectionReason', '')
                ->call('reject', $invoice->id)
                ->assertHasErrors(['rejectionReason' => 'required']);
    });

    it('handles invoice not found gracefully', function () {
        Livewire::actingAs($this->reviewer)
            .test(InvoiceApproval::class)
                ->call('submitForApproval', 999)
                ->assertDispatched('error', 'Invoice not found');
    });
});
