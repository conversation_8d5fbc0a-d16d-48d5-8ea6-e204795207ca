<?php

use App\Enums\UserRole;
use App\Models\Estate;
use App\Models\User;

uses(\Illuminate\Foundation\Testing\RefreshDatabase::class);

test('reviewer can access billing reports', function () {
    // Create admin user to assign estates
    $admin = User::factory()->create(['role' => UserRole::ADMIN]);

    // Create reviewer and estate
    $reviewer = User::factory()->create(['role' => UserRole::REVIEWER]);
    $estate = Estate::factory()->create();

    // Assign estate to reviewer
    $reviewer->assignedEstates()->attach($estate->id, ['assigned_by' => $admin->id]);

    $response = $this->actingAs($reviewer)->get('/reviewer/billing/reports');

    $response->assertStatus(200);
});

test('unauthenticated user cannot access billing reports', function () {
    $response = $this->get('/reviewer/billing/reports');

    $response->assertRedirect('/login');
});

test('user without permission cannot access billing reports', function () {
    // Create a manager user who doesn't have invoices.generate_assigned permission
    $manager = User::factory()->create(['role' => UserRole::MANAGER]);

    $response = $this->actingAs($manager)->get('/reviewer/billing/reports');

    $response->assertStatus(403);
});
