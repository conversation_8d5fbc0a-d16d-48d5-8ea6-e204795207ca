<?php

declare(strict_types=1);

use App\Enums\UserRole;
use App\Models\Estate;
use App\Models\House;
use App\Models\Invoice;
use App\Models\User;
use App\Services\AccountBalanceService;
use App\Services\BillingOperationService;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->accountBalanceService = new AccountBalanceService;
    $this->billingService = new BillingOperationService($this->accountBalanceService);
});

it('can generate bulk invoices for multiple houses', function () {
    $houses = House::factory()->count(3)->create();
    $user = User::factory()->create(['role' => UserRole::MANAGER]);

    $invoices = $this->billingService->generateBulkInvoices(
        $houses->pluck('id')->toArray(),
        1500.00,
        'August 2025 Water Bill',
        $user
    );

    expect($invoices)->toHaveCount(3);
    expect((float) $invoices[0]->amount)->toEqual(1500.0);
    expect($invoices[0]->notes)->toBe('August 2025 Water Bill');
});

it('can send bulk reminders for overdue invoices', function () {
    $user = User::factory()->create(['role' => UserRole::MANAGER]);
    $houses = House::factory()->count(2)->create();

    // Create overdue invoices using the billing service
    $invoices = [];
    foreach ($houses as $house) {
        $invoices[] = $this->billingService->generateBulkInvoices(
            [$house->id],
            1500,
            'Test Invoice',
            $user,
            now()->subDays(10)
        )[0];

        // Update status to overdue
        $invoices[count($invoices) - 1]->update(['status' => 'overdue']);
    }

    $result = $this->billingService->sendBulkReminders(
        collect($invoices)->pluck('id')->toArray(),
        'Payment Reminder',
        $user
    );

    expect($result['success'])->toBeTrue();
    expect($result['sent_count'])->toBe(2);
});

it('can bulk approve invoices', function () {
    $user = User::factory()->create(['role' => UserRole::REVIEWER]);
    $houses = House::factory()->count(2)->create();

    // Create draft invoices using the billing service
    $invoices = [];
    foreach ($houses as $house) {
        $invoice = $this->billingService->generateBulkInvoices(
            [$house->id],
            1500,
            'Test Invoice',
            $user
        )[0];

        // Keep status as draft (ready for approval)
        $invoices[] = $invoice;
    }

    $result = $this->billingService->bulkApproveInvoices(
        collect($invoices)->pluck('id')->toArray(),
        $user
    );

    expect($result['success'])->toBeTrue();
    expect($result['approved_count'])->toBe(2);

    foreach ($invoices as $invoice) {
        expect($invoice->fresh()->status)->toBe('sent');
        expect($invoice->fresh()->sent_at)->not()->toBeNull();
    }
});

it('can get billing summary for estate', function () {
    $estate = Estate::factory()->create();
    $houses = House::factory()->count(3)->create(['estate_id' => $estate->id]);
    $user = User::factory()->create(['role' => UserRole::MANAGER]);

    // Create different invoice statuses using billing service
    $invoice1 = $this->billingService->generateBulkInvoices(
        [$houses[0]->id],
        1000,
        'Paid Invoice',
        $user
    )[0];
    $invoice1->update(['status' => 'paid']);

    $invoice2 = $this->billingService->generateBulkInvoices(
        [$houses[1]->id],
        1500,
        'Overdue Invoice',
        $user
    )[0];
    $invoice2->update(['status' => 'overdue']);

    $invoice3 = $this->billingService->generateBulkInvoices(
        [$houses[2]->id],
        1200,
        'Draft Invoice',
        $user
    )[0];

    $summary = $this->billingService->getBillingSummary($estate->id);

    expect($summary['total_houses'])->toBe(3);
    expect($summary['total_invoices'])->toBe(3);
    expect($summary['paid_invoices'])->toBe(1);
    expect($summary['overdue_invoices'])->toBe(1);
    expect($summary['draft_invoices'])->toBe(1);
    expect((float) $summary['total_amount'])->toEqual(4000.0); // 3700 + 300 (3x100 fixed charge)
    expect((float) $summary['paid_amount'])->toEqual(1100.0); // 1000 + 100 fixed charge
    expect((float) $summary['overdue_amount'])->toEqual(1600.0); // 1500 + 100 fixed charge
});

it('can get aging analysis', function () {
    $houses = House::factory()->count(4)->create();
    $user = User::factory()->create(['role' => UserRole::MANAGER]);

    // Create invoices with different aging using billing service
    $invoice1 = $this->billingService->generateBulkInvoices(
        [$houses[0]->id],
        1000,
        '10 Days Overdue',
        $user,
        now()->subDays(10)
    )[0];
    $invoice1->update(['status' => 'overdue', 'due_date' => now()->subDays(10)]);

    $invoice2 = $this->billingService->generateBulkInvoices(
        [$houses[1]->id],
        1500,
        '40 Days Overdue',
        $user,
        now()->subDays(40)
    )[0];
    $invoice2->update(['status' => 'overdue', 'due_date' => now()->subDays(40)]);

    $invoice3 = $this->billingService->generateBulkInvoices(
        [$houses[2]->id],
        2000,
        '90 Days Overdue',
        $user,
        now()->subDays(90)
    )[0];
    $invoice3->update(['status' => 'overdue', 'due_date' => now()->subDays(90)]);

    $invoice4 = $this->billingService->generateBulkInvoices(
        [$houses[3]->id],
        2500,
        '120 Days Overdue',
        $user,
        now()->subDays(120)
    )[0];
    $invoice4->update(['status' => 'overdue', 'due_date' => now()->subDays(120)]);

    $aging = $this->billingService->getAgingAnalysis();

    expect((float) $aging['0_30_days'])->toEqual(1100.0); // 1000 + 100 fixed charge
    expect((float) $aging['31_60_days'])->toEqual(1600.0); // 1500 + 100 fixed charge
    expect((float) $aging['61_90_days'])->toEqual(0.0); // 90+ days goes to 90+ bucket
    expect((float) $aging['90_plus_days'])->toEqual(4700.0); // 2000 + 100 + 2500 + 100 fixed charge
    expect((float) $aging['total_overdue'])->toEqual(7400.0); // 7000 + 400 fixed charge
});

it('can bulk cancel invoices', function () {
    $user = User::factory()->create(['role' => UserRole::MANAGER]);
    $houses = House::factory()->count(2)->create();

    // Create draft invoices
    $invoices = [];
    foreach ($houses as $house) {
        $invoices[] = Invoice::factory()->create([
            'house_id' => $house->id,
            'status' => 'draft',
        ]);
    }

    $result = $this->billingService->bulkCancelInvoices(
        collect($invoices)->pluck('id')->toArray(),
        $user
    );

    expect($result['success'])->toBeTrue();
    expect($result['cancelled_count'])->toBe(2);

    foreach ($invoices as $invoice) {
        expect($invoice->fresh()->status)->toBe('cancelled');
    }
});

it('validates invoice status before bulk operations', function () {
    $user = User::factory()->create(['role' => UserRole::REVIEWER]);
    $houses = House::factory()->count(2)->create();

    // Create mixed status invoices using billing service
    $invoice1 = $this->billingService->generateBulkInvoices(
        [$houses[0]->id],
        1000,
        'Draft Invoice',
        $user
    )[0];
    // Keep as draft (can be approved)

    $invoice2 = $this->billingService->generateBulkInvoices(
        [$houses[1]->id],
        1500,
        'Sent Invoice',
        $user
    )[0];
    $invoice2->update(['status' => 'sent']); // Already sent, can't be approved again

    $result = $this->billingService->bulkApproveInvoices(
        [$invoice1->id, $invoice2->id],
        $user
    );

    expect($result['success'])->toBeTrue();
    expect($result['approved_count'])->toBe(1);
    expect($result['skipped_count'])->toBe(1);
});

it('handles empty arrays in bulk operations', function () {
    $user = User::factory()->create(['role' => UserRole::MANAGER]);

    $result = $this->billingService->generateBulkInvoices([], 1000, 'Test', $user);
    expect($result)->toBeEmpty();

    $result = $this->billingService->sendBulkReminders([], 'Test', $user);
    expect($result['success'])->toBeTrue();
    expect($result['sent_count'])->toBe(0);

    $result = $this->billingService->bulkApproveInvoices([], $user);
    expect($result['success'])->toBeTrue();
    expect($result['approved_count'])->toBe(0);
});
